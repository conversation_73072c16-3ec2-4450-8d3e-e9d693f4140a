<?php
include('webset.php');

echo "<h2>تحديث جدول offers لإضافة خاصية الإخفاء المؤقت</h2>";

try {
    // إضافة حقل الإخفاء المؤقت
    $sql = "ALTER TABLE offers 
            ADD COLUMN hidden_temp TINYINT(1) DEFAULT 0 COMMENT 'إخفاء مؤقت (0=ظاهر، 1=مخفي مؤقتاً)'";
    
    $db->exec($sql);
    echo "<div style='color: green; font-weight: bold; margin: 20px 0;'>✅ تم إضافة حقل hidden_temp بنجاح!</div>";
    
    // إضافة فهرس للبحث السريع
    $sql2 = "ALTER TABLE offers ADD INDEX idx_hidden_temp (hidden_temp)";
    $db->exec($sql2);
    echo "<div style='color: green; font-weight: bold; margin: 20px 0;'>✅ تم إضافة الفهرس بنجاح!</div>";
    
    // عرض هيكل الجدول الجديد
    $stmt = $db->query("DESCRIBE offers");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>هيكل جدول offers الجديد:</h3>";
    echo "<table border='1' style='border-collapse: collapse; margin: 20px 0;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th style='padding: 10px;'>اسم الحقل</th>";
    echo "<th style='padding: 10px;'>النوع</th>";
    echo "<th style='padding: 10px;'>Null</th>";
    echo "<th style='padding: 10px;'>Key</th>";
    echo "<th style='padding: 10px;'>Default</th>";
    echo "</tr>";
    
    foreach($columns as $column) {
        echo "<tr>";
        echo "<td style='padding: 8px;'>".$column['Field']."</td>";
        echo "<td style='padding: 8px;'>".$column['Type']."</td>";
        echo "<td style='padding: 8px;'>".$column['Null']."</td>";
        echo "<td style='padding: 8px;'>".$column['Key']."</td>";
        echo "<td style='padding: 8px;'>".$column['Default']."</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // عرض المدن المتاحة لاختيار الرياض
    $cities = getAllFrom('*', 'cities', 'WHERE status = 1', 'ORDER BY name ASC');
    echo "<h3>المدن المتاحة:</h3>";
    echo "<form method='POST' action=''>";
    echo "<select name='city_id' style='padding: 10px; margin: 10px;'>";
    echo "<option value=''>اختر المدينة لإخفاء رحلاتها مؤقتاً</option>";
    foreach($cities as $city) {
        echo "<option value='".$city['id']."'>".$city['name']."</option>";
    }
    echo "</select>";
    echo "<button type='submit' name='hide_city_offers' style='padding: 10px; background: #dc3545; color: white; border: none; cursor: pointer;'>إخفاء رحلات هذه المدينة مؤقتاً</button>";
    echo "</form>";
    
    // معالجة طلب إخفاء رحلات مدينة معينة
    if(isset($_POST['hide_city_offers']) && !empty($_POST['city_id'])) {
        $city_id = $_POST['city_id'];
        $stmt = $db->prepare("UPDATE offers SET hidden_temp = 1 WHERE catid = ? AND status = 1");
        $result = $stmt->execute([$city_id]);
        
        if($result) {
            $affected_rows = $stmt->rowCount();
            $city_name = getAllFrom('name', 'cities', 'WHERE id = ?', '', [$city_id]);
            $city_name = $city_name[0]['name'] ?? 'المدينة المحددة';
            echo "<div style='color: green; font-weight: bold; margin: 20px 0; padding: 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px;'>";
            echo "✅ تم إخفاء {$affected_rows} رحلة من {$city_name} مؤقتاً!<br>";
            echo "الرحلات ما زالت مفعلة في النظام لكنها مخفية من العرض.";
            echo "</div>";
        }
    }
    
    // عرض إحصائيات الرحلات
    $total_offers = getAllFrom('COUNT(*) as count', 'offers', '', '')[0]['count'];
    $active_offers = getAllFrom('COUNT(*) as count', 'offers', 'WHERE status = 1', '')[0]['count'];
    $hidden_temp_offers = getAllFrom('COUNT(*) as count', 'offers', 'WHERE hidden_temp = 1', '')[0]['count'];
    
    echo "<h3>إحصائيات الرحلات:</h3>";
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;'>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; text-align: center;'>";
    echo "<h4>إجمالي الرحلات</h4>";
    echo "<span style='font-size: 24px; font-weight: bold; color: #007bff;'>{$total_offers}</span>";
    echo "</div>";
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; text-align: center;'>";
    echo "<h4>الرحلات المفعلة</h4>";
    echo "<span style='font-size: 24px; font-weight: bold; color: #28a745;'>{$active_offers}</span>";
    echo "</div>";
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; text-align: center;'>";
    echo "<h4>الرحلات المخفية مؤقتاً</h4>";
    echo "<span style='font-size: 24px; font-weight: bold; color: #dc3545;'>{$hidden_temp_offers}</span>";
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    if(strpos($e->getMessage(), 'Duplicate column name') !== false) {
        echo "<div style='color: orange; font-weight: bold; margin: 20px 0;'>⚠️ الحقل موجود بالفعل!</div>";
        
        // عرض الإحصائيات حتى لو كان الحقل موجود
        $total_offers = getAllFrom('COUNT(*) as count', 'offers', '', '')[0]['count'];
        $active_offers = getAllFrom('COUNT(*) as count', 'offers', 'WHERE status = 1', '')[0]['count'];
        $hidden_temp_offers = getAllFrom('COUNT(*) as count', 'offers', 'WHERE hidden_temp = 1', '')[0]['count'];
        
        echo "<h3>إحصائيات الرحلات الحالية:</h3>";
        echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;'>";
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; text-align: center;'>";
        echo "<h4>إجمالي الرحلات</h4>";
        echo "<span style='font-size: 24px; font-weight: bold; color: #007bff;'>{$total_offers}</span>";
        echo "</div>";
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; text-align: center;'>";
        echo "<h4>الرحلات المفعلة</h4>";
        echo "<span style='font-size: 24px; font-weight: bold; color: #28a745;'>{$active_offers}</span>";
        echo "</div>";
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; text-align: center;'>";
        echo "<h4>الرحلات المخفية مؤقتاً</h4>";
        echo "<span style='font-size: 24px; font-weight: bold; color: #dc3545;'>{$hidden_temp_offers}</span>";
        echo "</div>";
        echo "</div>";
    } else {
        echo "<div style='color: red; font-weight: bold; margin: 20px 0;'>❌ خطأ: " . $e->getMessage() . "</div>";
    }
}

echo "<hr>";
echo "<h3>الخطوات التالية:</h3>";
echo "<ol>";
echo "<li>قم بتشغيل هذا الملف لإضافة الحقل الجديد</li>";
echo "<li>استخدم النموذج أعلاه لإخفاء رحلات الرياض مؤقتاً</li>";
echo "<li>سيتم تحديث ملفات العرض تلقائياً لإخفاء الرحلات المؤقتة</li>";
echo "<li>يمكنك إظهار الرحلات مرة أخرى عن طريق تغيير قيمة hidden_temp إلى 0</li>";
echo "</ol>";
?>
