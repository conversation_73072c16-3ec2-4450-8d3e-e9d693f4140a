-- إضافة حقل للإخفاء المؤقت للرحلات
-- هذا الحقل يسمح بإخفاء الرحلات من العرض مع الحفاظ على تفعيلها في النظام

ALTER TABLE offers 
ADD COLUMN hidden_temp TINYINT(1) DEFAULT 0 COMMENT 'إخفاء مؤقت (0=ظاهر، 1=مخفي مؤقتاً)';

-- إضافة فهرس للبحث السريع
ALTER TABLE offers 
ADD INDEX idx_hidden_temp (hidden_temp);

-- تحديث الرحلات الخاصة بالرياض لإخفائها مؤقتاً
-- يمكنك تغيير catid حسب معرف مدينة الرياض في نظامك
UPDATE offers 
SET hidden_temp = 1 
WHERE catid = (SELECT id FROM cities WHERE name LIKE '%الرياض%' LIMIT 1)
AND status = 1;
